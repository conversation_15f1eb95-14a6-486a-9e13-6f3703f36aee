// Login page functionality
class LoginManager {
    constructor() {
        this.form = document.getElementById('loginForm');
        this.usernameInput = document.getElementById('username');
        this.passwordInput = document.getElementById('password');
        this.submitButton = document.getElementById('submitBtn');
        this.loadingSpinner = document.querySelector('.loading-spinner');
        this.errorMessage = document.querySelector('.error-message');
        this.successMessage = document.querySelector('.success-message');
        
        this.init();
    }
    
    init() {
        this.bindEvents();
        this.setupInputValidation();
    }
    
    bindEvents() {
        // Form submission
        this.form.addEventListener('submit', (e) => this.handleSubmit(e));
        
        // Input events for real-time validation
        this.usernameInput.addEventListener('input', () => this.validateInput(this.usernameInput));
        this.passwordInput.addEventListener('input', () => this.validateInput(this.passwordInput));
        
        // Forgot password link
        document.querySelector('.forgot-password').addEventListener('click', (e) => {
            e.preventDefault();
            this.handleForgotPassword();
        });
    }
    
    setupInputValidation() {
        // Add pattern for username (alphanumeric and underscore)
        this.usernameInput.setAttribute('pattern', '^[a-zA-Z0-9_]{3,20}$');
        this.usernameInput.setAttribute('title', '用户名应为3-20个字符，只能包含字母、数字和下划线');
        
        // Add pattern for password (minimum 6 characters)
        this.passwordInput.setAttribute('minlength', '6');
        this.passwordInput.setAttribute('title', '密码至少需要6个字符');
    }
    
    validateInput(input) {
        const isValid = input.checkValidity();
        
        if (input.value.length > 0) {
            if (isValid) {
                input.style.borderColor = '#28a745';
            } else {
                input.style.borderColor = '#dc3545';
            }
        } else {
            input.style.borderColor = '#e1e5e9';
        }
        
        return isValid;
    }
    
    async handleSubmit(e) {
        e.preventDefault();
        
        const username = this.usernameInput.value.trim();
        const password = this.passwordInput.value;
        
        // Hide previous messages
        this.hideMessages();
        
        // Validate inputs
        if (!this.validateForm(username, password)) {
            return;
        }
        
        // Show loading state
        this.setLoadingState(true);
        
        try {
            // Simulate API call
            const result = await this.authenticateUser(username, password);
            
            if (result.success) {
                this.showSuccess(`欢迎回来，${username}！`);
                
                // Redirect after successful login (simulate)
                setTimeout(() => {
                    this.showSuccess('正在跳转到主页面...');
                    // window.location.href = '/dashboard';
                }, 1500);
            } else {
                this.showError(result.message || '登录失败，请检查用户名和密码');
            }
        } catch (error) {
            this.showError('网络错误，请稍后重试');
            console.error('Login error:', error);
        } finally {
            this.setLoadingState(false);
        }
    }
    
    validateForm(username, password) {
        if (!username) {
            this.showError('请输入用户名');
            this.usernameInput.focus();
            return false;
        }
        
        if (!password) {
            this.showError('请输入密码');
            this.passwordInput.focus();
            return false;
        }
        
        if (username.length < 3) {
            this.showError('用户名至少需要3个字符');
            this.usernameInput.focus();
            return false;
        }
        
        if (password.length < 6) {
            this.showError('密码至少需要6个字符');
            this.passwordInput.focus();
            return false;
        }
        
        return true;
    }
    
    async authenticateUser(username, password) {
        // Simulate API call delay
        await this.delay(2000);
        
        // Demo credentials (in real app, this would be handled by backend)
        const demoCredentials = {
            'admin': 'admin123',
            'user': 'user123',
            'test': 'test123'
        };
        
        if (demoCredentials[username] && demoCredentials[username] === password) {
            return { success: true, user: { username } };
        } else {
            return { success: false, message: '用户名或密码错误' };
        }
    }
    
    setLoadingState(isLoading) {
        if (isLoading) {
            this.submitButton.disabled = true;
            this.loadingSpinner.style.display = 'inline-block';
            this.submitButton.innerHTML = '<span class="loading-spinner"></span>登录中...';
        } else {
            this.submitButton.disabled = false;
            this.loadingSpinner.style.display = 'none';
            this.submitButton.innerHTML = '登录';
        }
    }
    
    showError(message) {
        this.errorMessage.textContent = message;
        this.errorMessage.classList.add('show');
        this.successMessage.classList.remove('show');
    }
    
    showSuccess(message) {
        this.successMessage.textContent = message;
        this.successMessage.classList.add('show');
        this.errorMessage.classList.remove('show');
    }
    
    hideMessages() {
        this.errorMessage.classList.remove('show');
        this.successMessage.classList.remove('show');
    }
    
    handleForgotPassword() {
        this.showError('忘记密码功能尚未实现，请联系管理员');
    }
    
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new LoginManager();
});

// Add some utility functions
const Utils = {
    // Debounce function for input validation
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    },
    
    // Check if device is mobile
    isMobile() {
        return window.innerWidth <= 768;
    },
    
    // Generate random ID
    generateId() {
        return Math.random().toString(36).substr(2, 9);
    }
};
